// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://pwaeknalhosfwuxkpaet.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";
// Create a custom fetch function with simplified retry logic
const customFetch = (...args: Parameters<typeof fetch>): Promise<Response> => {
  const MAX_RETRIES = 3; // Reduced to prevent infinite loops
  const INITIAL_RETRY_DELAY = 1000; // 1 second initial delay
  const MAX_RETRY_DELAY = 5000; // 5 seconds maximum delay
  let retryCount = 0;
  let isOffline = false;

  // Extract URL and method for better logging
  const url = typeof args[0] === 'string' ? args[0] : args[0] instanceof URL ? args[0].toString() : 'unknown';
  const method = args[1]?.method || 'GET';

  // Log the request for debugging
  console.log(`[Supabase] Fetch request: ${method} ${url}`);

  // Check if we're offline
  if (typeof navigator !== 'undefined' && 'onLine' in navigator) {
    isOffline = !navigator.onLine;
    if (isOffline) {
      console.warn('[Supabase] Network is offline, using cached data if available');
      // Dispatch network status event
      try {
        window.dispatchEvent(new CustomEvent('stayfu-network-status', {
          detail: { online: false, timestamp: new Date().toISOString() }
        }));
      } catch (e) {
        console.error('[Supabase] Error dispatching network status event:', e);
      }
    }
  }

  // Simplified session check - let Supabase handle session management
  let sessionExpired = false;

  // Function to create a mock response based on URL pattern
  const createMockResponse = (url: string): Response | null => {
    // For auth-related requests
    if (url.includes('/auth/')) {
      console.log('Creating mock response for auth request');
      // Try to get cached session from localStorage
      try {
        const cachedSession = localStorage.getItem('stayfu_auth_token');
        if (cachedSession) {
          const session = JSON.parse(cachedSession);
          return new Response(JSON.stringify({
            user: session.user || null,
            session: session
          }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          });
        }
      } catch (e) {
        console.error('Error parsing cached session:', e);
      }

      return new Response(JSON.stringify({
        user: null,
        session: null
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // For storage-related requests
    if (url.includes('/storage/')) {
      console.log('Creating mock response for storage request');
      return new Response(JSON.stringify({
        data: [],
        error: null
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // For data-related requests (tables)
    if (url.includes('/rest/v1/')) {
      console.log('Creating mock response for data request');
      // Try to get cached data from localStorage
      const cacheKey = `stayfu_cache_${url.replace(/[^a-zA-Z0-9]/g, '_')}`;
      try {
        const cachedData = localStorage.getItem(cacheKey);
        if (cachedData) {
          console.log(`Using cached data for ${url}`);
          return new Response(cachedData, {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          });
        }
      } catch (e) {
        console.error('Error retrieving cached data:', e);
      }

      // Return empty array as fallback
      return new Response(JSON.stringify([]), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // For RPC calls
    if (url.includes('/rpc/')) {
      console.log('Creating mock response for RPC request');
      return new Response(JSON.stringify([]), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // No mock available
    return null;
  };

  // If we're offline, try to return a mock response immediately
  if (isOffline) {
    const url = args[0].toString();
    const mockResponse = createMockResponse(url);
    if (mockResponse) {
      return Promise.resolve(mockResponse);
    }
  }

  const attemptFetch = (): Promise<Response> => {

    // Special handling for auth requests - no timeout for these
    const url = args[0].toString();
    if (url.includes('/auth/')) {
      console.log('[Supabase] Auth request detected - bypassing timeout');
      console.log('[Supabase] Auth request URL:', url);

      // For sign-in requests, log more details (but not the password)
      if (url.includes('/auth/v1/token') && args[1]?.method === 'POST' && args[1]?.body) {
        try {
          const bodyStr = args[1].body.toString();
          if (bodyStr) {
            const bodyData = JSON.parse(bodyStr);
            if (bodyData.email) {
              console.log('[Supabase] Auth request for email:', bodyData.email);
            }
          }
        } catch (e) {
          console.log('[Supabase] Could not parse auth request body');
        }
      }

      // Use a direct fetch with no timeout
      return fetch(...args).then(response => {
        console.log('[Supabase] Auth response status:', response.status);
        return response;
      }).catch(error => {
        console.error('[Supabase] Auth request failed:', error);
        throw error;
      });
    }

    // Check if this is an RPC call to get_profile
    if (url.includes('/rpc/get_profile')) {
      console.log('[Supabase] get_profile RPC call detected - using special handling');

      // Try to get the profile from localStorage first
      try {
        const cachedProfile = localStorage.getItem('stayfu_cached_profile');
        if (cachedProfile) {
          console.log('[Supabase] Using cached profile data');
          return Promise.resolve(new Response(cachedProfile, {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          }));
        }
      } catch (e) {
        console.error('[Supabase] Error retrieving cached profile:', e);
      }
    }

    // Add a timeout to the fetch to prevent hanging requests for non-auth requests
    const fetchPromise = fetch(...args);
    const timeoutPromise = new Promise<Response>((_, reject) => {
      setTimeout(() => {
        reject(new Error('Fetch timeout after 60 seconds'));
      }, 60000); // 60 second timeout (increased from 30)
    });

    return Promise.race([fetchPromise, timeoutPromise])
      .then(response => {
        // If the response is ok, cache it for offline use if it's a GET request
        if (response.ok) {
          const url = args[0].toString();
          const method = args[1]?.method || 'GET';

          if (method === 'GET' && (url.includes('/rest/v1/') || url.includes('/rpc/'))) {
            // Clone the response before using it
            response.clone().json().then(data => {
              try {
                const cacheKey = `stayfu_cache_${url.replace(/[^a-zA-Z0-9]/g, '_')}`;
                localStorage.setItem(cacheKey, JSON.stringify(data));

                // Store cache timestamp
                const cacheTimestamps = JSON.parse(localStorage.getItem('stayfu_cache_timestamps') || '{}');
                cacheTimestamps[cacheKey] = Date.now();
                localStorage.setItem('stayfu_cache_timestamps', JSON.stringify(cacheTimestamps));

                console.log(`[Supabase] Cached data for ${url}`);

                // Dispatch connection restored event if we were previously offline
                if (isOffline && navigator.onLine) {
                  try {
                    window.dispatchEvent(new CustomEvent('stayfu-network-status', {
                      detail: { online: true, timestamp: new Date().toISOString() }
                    }));
                    console.log('[Supabase] Network connection restored');
                    isOffline = false;
                  } catch (e) {
                    console.error('[Supabase] Error dispatching network status event:', e);
                  }
                }
              } catch (e) {
                console.error('[Supabase] Error caching response:', e);
              }
            }).catch(e => {
              console.error('[Supabase] Error parsing response for caching:', e);
            });
          }

          return response;
        }

        // If we've reached max retries, return the response anyway
        if (retryCount >= MAX_RETRIES) {
          console.warn(`Max retries (${MAX_RETRIES}) reached for ${args[0]}, returning response with status ${response.status}`);
          return response;
        }

        // Otherwise, retry with exponential backoff
        retryCount++;
        const delay = Math.min(INITIAL_RETRY_DELAY * Math.pow(2, retryCount - 1), MAX_RETRY_DELAY);
        console.log(`Retrying fetch (${retryCount}/${MAX_RETRIES}) after ${delay}ms...`);

        return new Promise(resolve => {
          setTimeout(() => resolve(attemptFetch()), delay);
        });
      })
      .catch(err => {
        console.error('[Supabase] Fetch error:', err);

        // Log more detailed information about the error
        const url = args[0].toString();
        console.error(`[Supabase] Failed request details: URL=${url}, Method=${args[1]?.method || 'GET'}, RetryCount=${retryCount}`);

        // Check if it's a "Failed to fetch" error, which indicates network issues
        const isNetworkError = err.message && (
          err.message.includes('Failed to fetch') ||
          err.message.includes('NetworkError') ||
          err.message.includes('Network request failed') ||
          err.message.includes('Fetch timeout')
        );

        if (isNetworkError) {
          console.warn('[Supabase] Network error detected, will attempt retry with backoff');

          // Update offline status and dispatch event
          if (typeof navigator !== 'undefined' && 'onLine' in navigator) {
            isOffline = !navigator.onLine;

            // Dispatch network status event if we're offline
            if (isOffline) {
              try {
                window.dispatchEvent(new CustomEvent('stayfu-network-status', {
                  detail: { online: false, timestamp: new Date().toISOString() }
                }));
              } catch (e) {
                console.error('[Supabase] Error dispatching network status event:', e);
              }
            }
          }

          // Store information about the failed request for diagnostics
          try {
            const failedRequests = JSON.parse(localStorage.getItem('stayfu_failed_requests') || '[]');
            failedRequests.push({
              url,
              time: new Date().toISOString(),
              error: err.message,
              retryCount,
              isOffline
            });

            // Keep only the last 20 failed requests
            if (failedRequests.length > 20) {
              failedRequests.shift();
            }

            localStorage.setItem('stayfu_failed_requests', JSON.stringify(failedRequests));
          } catch (e) {
            console.error('[Supabase] Error storing failed request info:', e);
          }
        }

        // Check if we're offline now
        const isCurrentlyOffline = typeof navigator !== 'undefined' && 'onLine' in navigator && !navigator.onLine;

        // If we're offline, try to return a mock response
        if (isCurrentlyOffline) {
          const mockResponse = createMockResponse(url);
          if (mockResponse) {
            console.log('[Supabase] Network is offline, using mock response');
            return mockResponse;
          }
        }

        // If we've reached max retries, try to return a mock response
        if (retryCount >= MAX_RETRIES) {
          const mockResponse = createMockResponse(url);

          if (mockResponse) {
            console.log(`[Supabase] Max retries (${MAX_RETRIES}) reached, using mock response`);
            return mockResponse;
          }

          // If no mock is available, throw the error
          console.error(`[Supabase] Max retries (${MAX_RETRIES}) reached and no mock available, throwing error`);

          // Dispatch a custom event to notify the application of a persistent fetch error
          if (typeof window !== 'undefined') {
            const errorEvent = new CustomEvent('stayfu-max-retries', {
              detail: {
                url,
                method: args[1]?.method || 'GET',
                error: err.message,
                time: new Date().toISOString(),
                isOffline: isOffline
              }
            });
            window.dispatchEvent(errorEvent);

            // Also dispatch the general fetch error event for backward compatibility
            const fetchErrorEvent = new CustomEvent('stayfu-fetch-error', {
              detail: {
                url,
                error: err.message,
                time: new Date().toISOString()
              }
            });
            window.dispatchEvent(fetchErrorEvent);
          }

          throw err;
        }

        // Otherwise, retry with exponential backoff
        retryCount++;
        const delay = Math.min(INITIAL_RETRY_DELAY * Math.pow(2, retryCount - 1), MAX_RETRY_DELAY);
        console.log(`[Supabase] Retrying fetch after error (${retryCount}/${MAX_RETRIES}) in ${delay}ms...`);

        return new Promise(resolve => {
          setTimeout(() => resolve(attemptFetch()), delay);
        });
      });
  };

  return attemptFetch();
};

// Create a custom storage implementation that persists sessions longer
const customStorage = {
  getItem: (key: string): string | null => {
    try {
      // First try localStorage
      const item = localStorage.getItem(key);
      if (item) {
        // If we have a session token, check if it's expired
        if (key === 'stayfu_auth_token') {
          try {
            const session = JSON.parse(item);
            if (session && session.expires_at) {
              const now = Math.floor(Date.now() / 1000);
              // If the token is expired, try to use the refresh token
              if (session.expires_at < now) {
                console.log('Session token expired, will attempt refresh');
              }
            }
          } catch (e) {
            console.error('Error parsing session token:', e);
          }
        }
        return item;
      }

      // If not in localStorage, try sessionStorage as fallback
      return sessionStorage.getItem(key);
    } catch (error) {
      console.error('Error getting item from storage:', error);
      return null;
    }
  },

  setItem: (key: string, value: string): void => {
    try {
      // Store in both localStorage and sessionStorage for redundancy
      localStorage.setItem(key, value);

      // For session tokens, also store in sessionStorage as backup
      if (key === 'stayfu_auth_token') {
        try {
          // Store a flag indicating we have a valid session
          localStorage.setItem('stayfu_has_session', 'true');

          // Parse the session to get expiry time
          const session = JSON.parse(value);
          if (session && session.expires_at) {
            const expiresAt = new Date(session.expires_at * 1000).toISOString();
            localStorage.setItem('stayfu_session_expires', expiresAt);
          }

          // Also store in sessionStorage as backup
          sessionStorage.setItem(key, value);
        } catch (e) {
          console.error('Error processing session token:', e);
        }
      }
    } catch (error) {
      console.error('Error setting item in storage:', error);
      // Try sessionStorage as fallback
      try {
        sessionStorage.setItem(key, value);
      } catch (e) {
        console.error('Error setting item in sessionStorage:', e);
      }
    }
  },

  removeItem: (key: string): void => {
    try {
      localStorage.removeItem(key);
      sessionStorage.removeItem(key);

      // Also clear related session flags
      if (key === 'stayfu_auth_token') {
        localStorage.removeItem('stayfu_has_session');
        localStorage.removeItem('stayfu_session_expires');
      }
    } catch (error) {
      console.error('Error removing item from storage:', error);
    }
  }
};

// Utility function to clean up old cache entries
const cleanupCache = () => {
  try {
    // Get cache timestamps
    const cacheTimestamps = JSON.parse(localStorage.getItem('stayfu_cache_timestamps') || '{}');
    const now = Date.now();
    const MAX_CACHE_AGE = 24 * 60 * 60 * 1000; // 24 hours
    const MAX_CACHE_ENTRIES = 50; // Maximum number of cache entries

    // Find old cache entries
    const entries = Object.entries(cacheTimestamps).map(([key, timestamp]) => ({
      key,
      timestamp: timestamp as number,
      age: now - (timestamp as number)
    }));

    // Sort by age (oldest first)
    entries.sort((a, b) => b.age - a.age);

    // Remove old entries and entries beyond the maximum
    let removedCount = 0;
    entries.forEach((entry, index) => {
      if (entry.age > MAX_CACHE_AGE || index >= MAX_CACHE_ENTRIES) {
        localStorage.removeItem(entry.key);
        delete cacheTimestamps[entry.key];
        removedCount++;
      }
    });

    // Save updated timestamps
    if (removedCount > 0) {
      localStorage.setItem('stayfu_cache_timestamps', JSON.stringify(cacheTimestamps));
      console.log(`Cleaned up ${removedCount} old cache entries`);
    }
  } catch (e) {
    console.error('Error cleaning up cache:', e);
  }
};

// Run cache cleanup on startup
cleanupCache();

// Add network status event listeners
if (typeof window !== 'undefined') {
  window.addEventListener('online', () => {
    console.log('Network connection restored, refreshing data');
    // Trigger a global data refresh if the window.refreshAllData function exists
    if (window.refreshAllData && typeof window.refreshAllData === 'function') {
      window.refreshAllData();
    }
  });

  window.addEventListener('offline', () => {
    console.log('Network connection lost, using cached data');
  });
}

export const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    persistSession: true,
    storageKey: 'stayfu_auth_token',
    autoRefreshToken: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
    // Set a longer session lifetime to match the server-side configuration
    localStorage: customStorage,
    // Increase the refresh interval to prevent token expiration
    autoRefreshTimeBeforeExpiry: 300, // Refresh 5 minutes before expiry (more reasonable)
    debug: false, // Disable debug mode for production
    // Add event handler for auth state changes
    onAuthStateChange: (event, session) => {
      console.log('[Supabase] Auth state changed:', event);

      // When token is refreshed, trigger a data refresh
      if (event === 'TOKEN_REFRESHED' && session) {
        console.log('[Supabase] Token refreshed successfully, triggering data refresh');

        // Store the refresh time
        try {
          localStorage.setItem('stayfu_last_token_refresh', JSON.stringify({
            time: new Date().toISOString(),
            expiresAt: session.expires_at ? new Date(session.expires_at * 1000).toISOString() : 'unknown'
          }));
        } catch (e) {
          console.error('[Supabase] Error storing token refresh time:', e);
        }

        // Trigger a global data refresh
        setTimeout(() => {
          if (window.refreshAllData && typeof window.refreshAllData === 'function') {
            console.log('[Supabase] Calling window.refreshAllData() after token refresh');
            window.refreshAllData();
          }

          // Dispatch a custom event that components can listen for
          try {
            window.dispatchEvent(new CustomEvent('stayfu-token-refreshed'));
            console.log('[Supabase] Dispatched stayfu-token-refreshed event');
          } catch (eventError) {
            console.error('[Supabase] Error dispatching token refreshed event:', eventError);
          }
        }, 500);
      }
    }
  },
  global: {
    fetch: customFetch
  },
  // Use our custom storage implementation
  storage: customStorage
});
