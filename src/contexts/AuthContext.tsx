import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { AuthState, User, UserProfile, UserRole, Session } from '@/types/auth';
import { toast } from 'sonner';

interface AuthContextProps {
  authState: AuthState;
  signUp: (email: string, password: string, firstName: string, lastName: string, role?: UserRole) => Promise<{ error: any | null; emailConfirmation?: boolean; user?: User }>;
  signIn: (email: string, password: string) => Promise<{ error: any | null; user?: User; session?: Session }>;
  signOut: () => Promise<void>;
  startImpersonation: (userId: string) => Promise<void>;
  stopImpersonation: () => Promise<void>;
  refreshProfile: () => Promise<void>;
  isImpersonationActive: () => boolean;
}

export const AuthContext = createContext<AuthContextProps | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    profile: null,
    isLoading: true,
    isAuthenticated: false,
    error: null,
    session: null,
    isImpersonating: false,
    originalUser: null,
  });

  const fetchProfile = async (userId: string): Promise<UserProfile | null> => {
    try {
      // First, try to get the profile using our RPC function
      try {
        console.log('Fetching profile using get_profile RPC function');
        const { data: profileData, error: profileError } = await supabase.rpc(
          'get_profile',
          { p_user_id: userId }
        );

        if (!profileError && profileData && profileData.length > 0) {
          console.log('Profile found using RPC function:', profileData[0]);
          return profileData[0] as UserProfile;
        }

        // If profile doesn't exist or there was an error, log it
        if (profileError) {
          console.error('Error fetching profile using RPC function:', profileError);
        } else if (!profileData || profileData.length === 0) {
          console.log('Profile not found using RPC function, will create one');
        }
      } catch (rpcError) {
        console.error('Exception fetching profile using RPC function:', rpcError);
      }

      // Fallback: try to get the profile directly from the database
      try {
        console.log('Falling back to direct database query');
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .single();

        if (!profileError && profileData) {
          console.log('Profile found in database:', profileData);
          return profileData as UserProfile;
        }

        // If profile doesn't exist, we'll try to create it
        if (profileError && profileError.code === 'PGRST116') {
          console.log('Profile not found in database, will create one');
        } else {
          console.error('Error fetching profile from database:', profileError);
        }
      } catch (dbError) {
        console.error('Exception fetching profile from database:', dbError);
      }

      // Try to get admin status
      let isAdminData = false;
      try {
        const { data, error } = await supabase.rpc('is_super_admin', {
          uid: userId
        });

        if (error) {
          console.error('Error checking admin status:', error);
        } else {
          isAdminData = data === true;
        }
      } catch (adminError) {
        console.error('Network error checking admin status:', adminError);
      }

      // Try to get user data
      try {
        const { data: userData } = await supabase.auth.getUser();

        if (userData?.user) {
          const userRole = userData.user.user_metadata?.role || 'property_manager';
          const firstName = userData.user.user_metadata?.first_name || '';
          const lastName = userData.user.user_metadata?.last_name || '';
          const email = userData.user.email || '';

          // Create a profile using our RPC function
          try {
            console.log('Attempting to create profile using create_profile RPC function');
            const { data: profileData, error: profileError } = await supabase.rpc(
              'create_profile',
              {
                p_id: userId,
                p_email: email,
                p_first_name: firstName,
                p_last_name: lastName,
                p_role: isAdminData ? 'super_admin' : userRole,
                p_is_super_admin: isAdminData
              }
            );

            if (profileError) {
              console.error('Error creating profile using RPC function:', profileError);
              // Continue with fallback approach
            } else if (profileData && profileData.length > 0) {
              console.log('Profile created successfully using RPC function:', profileData[0]);

              // Create a profile object from the RPC result
              const profile = profileData[0] as UserProfile;

              // If this is a property manager, also create a default team
              if (userRole === 'property_manager') {
                try {
                  // Check if user already has a team
                  const { data: existingTeams, error: teamsError } = await supabase
                    .from('teams')
                    .select('id')
                    .eq('owner_id', userId);

                  // Process team data if needed
                  if (teamsError) {
                    console.error('Error checking for existing teams:', teamsError);
                  }
                } catch (teamError) {
                  console.error('Exception checking for existing teams:', teamError);
                }

                return profile; // Return the profile early since we created it successfully
              }
            }
          } catch (rpcError) {
            console.error('Exception creating profile using RPC function:', rpcError);
          }

          // Fallback: Create a profile object and insert directly
          const profile = {
            id: userId,
            email: email,
            first_name: firstName,
            last_name: lastName,
            avatar_url: undefined, // Consider fetching from storage if available
            is_super_admin: isAdminData,
            role: isAdminData ? 'super_admin' : userRole,
            created_at: userData.user.created_at,
            updated_at: userData.user.updated_at || userData.user.created_at // Use created_at if updated_at is null
          };

          // Try to create the profile in the database directly
          try {
            console.log('Attempting to create profile in database directly:', profile);
            const { error: insertError } = await supabase
              .from('profiles')
              .upsert(profile);

            if (insertError) {
              console.error('Error creating profile in database directly:', insertError);
              // Continue with the in-memory profile even if database insert fails
            } else {
              console.log('Profile created successfully in database directly');

              // If this is a property manager, also create a default team
              if (userRole === 'property_manager') {
                try {
                  // Check if user already has a team
                  const { data: existingTeams, error: teamsError } = await supabase
                    .from('teams')
                    .select('id')
                    .eq('owner_id', userId);

                  if (teamsError) {
                    console.error('Error checking for existing teams:', teamsError);
                  } else if (!existingTeams || existingTeams.length === 0) {
                    // Create a default team
                    const teamName = `${firstName || 'User'}'s Team`;
                    const { error: teamError } = await supabase
                      .from('teams')
                      .insert({
                        name: teamName,
                        owner_id: userId,
                        created_at: new Date(),
                        updated_at: new Date()
                      });

                    if (teamError) {
                      console.error('Error creating default team:', teamError);
                    } else {
                      console.log('Default team created successfully');
                    }
                  }
                } catch (teamError) {
                  console.error('Exception creating default team:', teamError);
                }
              }
            }
          } catch (insertError) {
            console.error('Exception creating profile in database:', insertError);
          }

          return profile;
        }
      } catch (userError) {
        console.error('Error getting user data:', userError);

        // Try to create a minimal profile from local storage as fallback
        try {
          const localSession = localStorage.getItem('stayfu_auth_token');
          if (localSession) {
            const parsedSession = JSON.parse(localSession);
            if (parsedSession?.user) {
              console.log('Creating minimal profile from local session');
              const profile = {
                id: userId,
                email: parsedSession.user.email || '',
                first_name: parsedSession.user.user_metadata?.first_name || '',
                last_name: parsedSession.user.user_metadata?.last_name || '',
                avatar_url: undefined,
                is_super_admin: isAdminData,
                role: isAdminData ? 'super_admin' : (parsedSession.user.user_metadata?.role || 'property_manager'),
                created_at: parsedSession.user.created_at || new Date().toISOString(),
                updated_at: parsedSession.user.updated_at || new Date().toISOString()
              };

              // Try to create the profile in the database
              try {
                console.log('Attempting to create profile in database from local session:', profile);
                const { error: insertError } = await supabase
                  .from('profiles')
                  .upsert(profile);

                if (insertError) {
                  console.error('Error creating profile in database from local session:', insertError);
                } else {
                  console.log('Profile created successfully in database from local session');
                }
              } catch (insertError) {
                console.error('Exception creating profile in database from local session:', insertError);
              }

              return profile;
            }
          }
        } catch (fallbackError) {
          console.error('Error creating fallback profile:', fallbackError);
        }
      }

      // If all else fails, try to create a profile using the fix-missing-profiles function
      try {
        console.log('Attempting to fix missing profile using edge function');
        const { data: fixResult, error: fixError } = await supabase.functions.invoke(
          'fix-missing-profiles'
        );

        if (fixError) {
          console.error('Error fixing profiles:', fixError);
        } else {
          console.log('Profile fix attempted:', fixResult);

          // Try fetching the profile again
          const { data: refreshedProfile, error: refreshError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', userId)
            .single();

          if (!refreshError && refreshedProfile) {
            console.log('Profile created successfully by fix-missing-profiles');
            return refreshedProfile as UserProfile;
          }
        }
      } catch (fixException) {
        console.error('Exception fixing profiles:', fixException);
      }

      return null;
    } catch (error) {
      console.error('Error in fetchProfile:', error);
      return null;
    }
  };

  const refreshProfile = async (): Promise<void> => {
    try {
      // Always check for a session first, regardless of authState
      let sessionData, sessionError;

      try {
        const result = await supabase.auth.getSession();
        sessionData = result.data;
        sessionError = result.error;
      } catch (fetchError) {
        console.error('Network error refreshing session:', fetchError);
        // Try to get session from local storage as fallback
        const localSession = localStorage.getItem('stayfu_auth_token');
        if (localSession) {
          try {
            const parsedSession = JSON.parse(localSession);
            console.log('Using session from local storage as fallback');
            sessionData = { session: parsedSession };
            sessionError = null;
          } catch (parseError) {
            console.error('Error parsing local session:', parseError);
            sessionError = new Error('Failed to parse local session');
          }
        } else {
          sessionError = new Error('No session available and network request failed');
        }
      }

      if (sessionError) {
        console.error('Error refreshing session:', sessionError);
        // Don't throw, just return - this is a recoverable error
        return;
      }

      if (!sessionData.session) {
        console.log('No active session found when refreshing profile');
        // If we thought we were logged in but have no session, update auth state
        if (authState.isAuthenticated) {
          console.log('Auth state shows authenticated but no session exists, updating state');
          setAuthState({
            user: null,
            session: null,
            profile: null,
            isLoading: false,
            isAuthenticated: false,
            error: null,
            isImpersonating: false,
            originalUser: null,
          });
        }
        return;
      }

      // We have a valid session, proceed with profile fetch
      let profile = await fetchProfile(sessionData.session.user.id);

      if (!profile) {
        console.warn('Could not fetch profile, using fallback method');

        try {
          const { data: isAdminData } = await supabase.rpc('is_super_admin', {
            uid: sessionData.session.user.id
          });

          if (isAdminData !== undefined) {
            // Use actual timestamps in fallback as well
            profile = {
              id: sessionData.session.user.id,
              email: sessionData.session.user.email || '',
              first_name: sessionData.session.user.user_metadata?.first_name || '',
              last_name: sessionData.session.user.user_metadata?.last_name || '',
              avatar_url: undefined,
              is_super_admin: isAdminData,
              role: isAdminData ? 'super_admin' : 'property_manager',
              created_at: sessionData.session.user.created_at,
              updated_at: sessionData.session.user.updated_at || sessionData.session.user.created_at
            };
          }
        } catch (rpcError) {
          console.error('Error in fallback profile method:', rpcError);
          // Continue with null profile rather than failing completely
        }
      }

      // If we still don't have a profile, create a minimal one
      if (!profile && sessionData.session?.user) {
        console.warn('Creating minimal profile from session data');
        profile = {
          id: sessionData.session.user.id,
          email: sessionData.session.user.email || '',
          first_name: sessionData.session.user.user_metadata?.first_name || '',
          last_name: sessionData.session.user.user_metadata?.last_name || '',
          avatar_url: undefined,
          is_super_admin: false,
          role: 'property_manager', // Default role
          created_at: sessionData.session.user.created_at,
          updated_at: sessionData.session.user.updated_at || sessionData.session.user.created_at
        };
      }

      // Only update state if we have a valid session and at least a minimal profile
      if (sessionData.session?.user && profile) {
        setAuthState(prev => ({
          ...prev,
          user: sessionData.session?.user || prev.user,
          session: sessionData.session || prev.session,
          profile,
          isAuthenticated: true,
          error: null
        }));

        console.log('Profile refreshed successfully');
      } else {
        console.warn('Profile refresh failed - missing user or profile data');
      }
    } catch (error) {
      console.error('Error in refreshProfile:', error);
      // Don't show toast here - it's too disruptive for a background operation
      // Just log the error and continue
    }
  };

  // Track if we've already initialized auth with a regular variable
  let authInitialized = false;

  useEffect(() => {
    const initializeAuth = async () => {
      // Prevent multiple initializations
      if (authInitialized) {
        console.log('Auth already initialized, skipping');
        return;
      }

      try {
        console.log('Initializing auth state...');
        setAuthState(prev => ({ ...prev, isLoading: true }));

        // Mark as initialized
        authInitialized = true;

        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Error getting session:', error);
          throw error;
        }

        if (session && session.user) {
          console.log('Session found, user is authenticated:', session.user.email);
          const profile = await fetchProfile(session.user.id);

          setAuthState({
            user: session.user,
            session,
            profile,
            isLoading: false,
            isAuthenticated: true,
            error: null,
            isImpersonating: false,
            originalUser: null,
          });
        } else {
          console.log('No active session found');
          setAuthState(prev => ({ ...prev, isLoading: false, isAuthenticated: false, error: null, session: null }));
        }

        // Set up auth state change listener
        const { data: { subscription } } = await supabase.auth.onAuthStateChange(
          async (event, session) => {
            console.log('Auth state changed:', event, session?.user?.email);

            if (session && session.user) {
              // Don't set loading to true here to avoid UI flicker during navigation
              const profile = await fetchProfile(session.user.id);
              setAuthState({
                user: session.user,
                session: session,
                profile,
                isLoading: false,
                isAuthenticated: true,
                error: null,
                isImpersonating: false,
                originalUser: null,
              });

              // If this is a sign-in event, we might need to redirect
              if (event === 'SIGNED_IN') {
                console.log('User signed in, profile loaded');
              }
            } else {
              // For sign out, we want to clear everything
              if (event === 'SIGNED_OUT') {
                console.log('User signed out, clearing auth state');
                // Clear any cached data
                localStorage.removeItem('lastFetchedData');
                sessionStorage.clear();
              }

              setAuthState({
                user: null,
                session: null,
                profile: null,
                isLoading: false,
                isAuthenticated: false,
                error: null,
                isImpersonating: false,
                originalUser: null,
              });
            }
          }
        );

        return () => {
          console.log('Cleaning up auth subscription');
          subscription.unsubscribe();
        };
      } catch (error) {
        console.error('Error initializing auth:', error);
        setAuthState(prev => ({ ...prev, isLoading: false, error: error instanceof Error ? error.message : String(error) }));
      }
    };

    initializeAuth();
  }, []);

  const signUp = async (
    email: string,
    password: string,
    firstName: string,
    lastName: string,
    role: UserRole = 'property_manager'
  ) => {
    try {
      // Check if this is for a pending invitation
      const pendingInvitation = localStorage.getItem('pendingInvitation');
      const pendingEmail = localStorage.getItem('pendingInvitationEmail');
      const pendingRole = localStorage.getItem('pendingInvitationRole');

      // Use the role from the invitation if available
      const effectiveRole = (pendingRole && ['service_provider', 'staff', 'property_manager'].includes(pendingRole as UserRole))
        ? pendingRole as UserRole
        : role;

      // For service providers or invitation signups, we'll auto-confirm the email
      let emailConfirmation = (effectiveRole === 'service_provider' || effectiveRole === 'staff' || pendingInvitation)
        ? false  // Disable email confirmation for service providers, staff, and all invitations
        : true;  // Keep email confirmation for regular signups

      console.log(`Signup details: role=${effectiveRole}, pendingInvitation=${pendingInvitation}, emailConfirmation=${emailConfirmation}`);

      // First, check if the user already exists by trying to sign in
      // This helps avoid the 500 error when trying to create a user that already exists
      let existingUser = false;
      let data, error;

      try {
        console.log('Checking if user already exists by attempting sign in');
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email,
          password
        });

        if (!signInError && signInData.user) {
          console.log('User already exists and credentials are valid, using existing user');
          data = signInData;
          existingUser = true;
          emailConfirmation = false; // No need for email confirmation
        } else {
          console.log('User does not exist or credentials are invalid, will create new user');
          // If sign-in fails, it's likely because the user doesn't exist, which is expected
          // We'll proceed with the signup flow
        }
      } catch (signInError) {
        console.error('Error checking if user exists:', signInError);
        // Continue with signup flow
      }

      // If user doesn't exist, create a new one
      if (!existingUser) {
        console.log(`Creating new user with role: ${effectiveRole}, email confirmation: ${emailConfirmation}`);

        try {
          // Regular signup with email confirmation
          const result = await supabase.auth.signUp({
            email,
            password,
            options: {
              data: {
                first_name: firstName,
                last_name: lastName,
                role: effectiveRole,
              },
              emailRedirectTo: window.location.origin + '/auth',
            },
          });

          data = result.data;
          error = result.error;

          // If we get a database error, it might be because the user already exists
          if (error && error.message.includes('Database error saving new user')) {
            console.log('User might already exist despite sign-in check, trying to sign in again');

            // Try to sign in with the provided credentials
            const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
              email,
              password
            });

            if (!signInError && signInData.user) {
              console.log('Successfully signed in existing user on second attempt');
              data = signInData;
              error = null;
              emailConfirmation = false; // No need for email confirmation
            } else {
              console.error('Failed to sign in existing user on second attempt:', signInError);
              // Keep the original error if sign-in fails
            }
          }
        } catch (signupError) {
          console.error('Error during signup process:', signupError);
          error = signupError;
        }
      }

      if (error) {
        toast.error(error.message);
        return { error };
      }

      // For service providers, create their profile if we didn't use the Edge Function
      // The Edge Function already creates the profile, so we only need to do this for fallback cases
      if (role === 'service_provider' && data?.user && emailConfirmation !== false) {
        try {
          // First try with service role to bypass RLS
          const { error: profileError } = await supabase.rpc(
            'create_service_provider_profile',
            {
              p_id: data.user.id,
              p_email: email,
              p_first_name: firstName,
              p_last_name: lastName,
              p_status: 'active'
            }
          );

          if (profileError) {
            console.error('Error creating service provider profile with RPC:', profileError);
            console.log('Continuing despite profile creation error - user may need to log in again');
            // We'll continue despite the error, as the user was created successfully
            // They may need to log in again to get the profile created
          }
        } catch (err) {
          console.error('Exception creating service provider profile:', err);
          console.log('Continuing despite profile creation error - user may need to log in again');
          // We'll continue despite the error, as the user was created successfully
          // They may need to log in again to get the profile created
        }
      }

      // If this is an invitation signup, we'll handle it as if email is already confirmed
      if (pendingInvitation && pendingEmail === email) {
        console.log('This is an invitation signup, treating as pre-confirmed');
        // We'll proceed with the flow as if the email is confirmed
        // The actual confirmation will happen when they click the link in their email
      }

      return { error: null, emailConfirmation, user: data?.user };
    } catch (error: any) {
      toast.error(error.message);
      return { error };
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      console.log('Signing in user:', email);
      setAuthState(prev => ({ ...prev, isLoading: true }));

      // Add timestamp for performance tracking
      const startTime = new Date().getTime();
      console.log(`Auth attempt started at: ${new Date().toISOString()}`);

      // Direct authentication without timeout
      console.log('Auth request sent to Supabase - no timeout');

      // Call Supabase directly without a timeout
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      const endTime = new Date().getTime();
      console.log(`Auth response received after ${endTime - startTime}ms`);

      if (error) {
        console.error('Sign in error:', error);

        // Provide more specific error messages based on the error type
        console.log('Auth error details:', error);

        if (error.message.includes('Invalid login credentials')) {
          toast.error('Incorrect email or password. Please check your credentials and try again.');
        } else if (error.message.includes('Email not confirmed')) {
          toast.error('Please verify your email address before logging in. Check your inbox for a confirmation email.');
        } else if (error.message.includes('taking longer than expected')) {
          toast.error('Connection issue detected. Please check your internet connection and try again.');
        } else if (error.message.includes('timed out')) {
          toast.error('Connection issue detected. Please check your internet connection and try again.');
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          toast.error('Network error. Please check your internet connection and try again.');
        } else {
          // Log the full error for debugging
          console.error('Unhandled auth error:', JSON.stringify(error, null, 2));
          toast.error('Login failed. Please try again in a few moments.');
        }

        // Make sure to reset loading state
        setAuthState(prev => ({ ...prev, isLoading: false, error }));
        return { error };
      }

      console.log('Sign in successful, fetching profile...');
      const profile = await fetchProfile(data.user.id);

      // Update auth state with the new session and user data
      setAuthState({
        user: data.user,
        session: data.session,
        profile,
        isLoading: false,
        isAuthenticated: true,
        error: null,
        isImpersonating: false,
        originalUser: null,
      });

      console.log('Auth state updated after sign in');
      return { error: null, user: data.user, session: data.session };
    } catch (error: any) {
      console.error('Exception during sign in:', error);

      // Log the full error for debugging
      console.error('Exception during sign in - Full details:', JSON.stringify(error, null, 2));

      // Provide more specific error messages based on the error type
      if (error.message?.includes('taking longer than expected')) {
        toast.error('Connection issue detected. Please check your internet connection and try again.');
      } else if (error.message?.includes('timed out')) {
        toast.error('Connection issue detected. Please check your internet connection and try again.');
      } else if (error.message?.includes('network') || error.message?.includes('fetch')) {
        toast.error('Network error. Please check your internet connection and try again.');
      } else if (error.message?.includes('Invalid login credentials')) {
        toast.error('Incorrect email or password. Please check your credentials and try again.');
      } else if (error.message?.includes('Email not confirmed')) {
        toast.error('Please verify your email address before logging in. Check your inbox for a confirmation email.');
      } else {
        toast.error('Login failed. Please try again in a few moments.');
      }

      // Always make sure to reset loading state
      setAuthState(prev => ({ ...prev, isLoading: false, error }));
      return { error: error };
    }
  };

  const signOut = async () => {
    try {
      console.log('Signing out user...');
      setAuthState(prev => ({ ...prev, isLoading: true }));

      // Clear service worker cache if available
      if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
        try {
          console.log('Clearing service worker cache...');
          const messageChannel = new MessageChannel();
          navigator.serviceWorker.controller.postMessage({
            type: 'CLEAR_CACHE'
          }, [messageChannel.port2]);
        } catch (e) {
          console.error('Failed to communicate with service worker:', e);
        }
      }

      // Handle impersonation case
      if (authState.isImpersonating && authState.originalUser) {
        console.log('Ending impersonation session...');
        setAuthState({
          user: authState.originalUser,
          session: null,
          profile: null,
          isLoading: false,
          isAuthenticated: true,
          error: null,
          isImpersonating: false,
          originalUser: null,
        });

        toast.success("Returned to original user account");
        return;
      }

      // Clear all auth-related data
      console.log('Clearing cached application data...');
      localStorage.removeItem('lastFetchedData');
      localStorage.removeItem('stayfu_auth_token');
      localStorage.removeItem('supabase.auth.token');
      localStorage.removeItem('stayfu_has_session');
      localStorage.removeItem('stayfu_session_expires');
      localStorage.removeItem('stayfu_last_token_refresh');
      localStorage.removeItem('stayfu_last_session_refresh');
      localStorage.removeItem('stayfu_session_needs_refresh');
      localStorage.removeItem('stayfu_force_session_refresh');
      localStorage.removeItem('stayfu_session_expired');
      localStorage.removeItem('stayfu_token_refreshed');

      // Clear all cache items
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('cache:') ||
            key.startsWith('stayfu_cache_') ||
            key.includes('supabase') ||
            key.includes('session')) {
          localStorage.removeItem(key);
        }
      });

      sessionStorage.clear();

      // Regular sign out - wrapped in try/catch to handle AuthSessionMissingError
      console.log('Calling Supabase signOut...');

      // Clear any Supabase cookies that might be causing issues
      document.cookie = 'sb-access-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
      document.cookie = 'sb-refresh-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

      try {
        // First check if we have a valid session before attempting to sign out
        const { data: sessionData } = await supabase.auth.getSession();

        if (sessionData && sessionData.session) {
          // We have a valid session, proceed with sign out
          try {
            // Try with local scope first (less likely to cause 403 errors)
            console.log('Trying local scope signOut first...');
            const localSignOutResult = await supabase.auth.signOut({ scope: 'local' });

            if (!localSignOutResult.error) {
              console.log('Local scope signOut successful');
            } else {
              console.warn('Warning from local Supabase signOut:', localSignOutResult.error);

              // If local scope fails but not with a 403, try global scope
              if (!localSignOutResult.error.message?.includes('403')) {
                console.log('Trying global scope signOut as fallback...');
                try {
                  const { error } = await supabase.auth.signOut({ scope: 'global' });
                  if (error) {
                    console.warn('Warning from global Supabase signOut:', error);
                  }
                } catch (globalError) {
                  console.warn('Error during global Supabase signOut call:', globalError);
                  // Continue with the sign out process regardless
                }
              }
            }
          } catch (innerError) {
            console.warn('Error during Supabase signOut call:', innerError);
            // Continue with the sign out process regardless
          }
        } else {
          // No valid session found, just log and continue
          console.log('No active session found, skipping Supabase signOut call');
        }
      } catch (signOutError) {
        // If we get AuthSessionMissingError or 403 Forbidden, the session is already gone or invalid
        // This is actually fine for our purposes, so we'll just log it and continue
        console.warn('Error during Supabase signOut (continuing anyway):', signOutError);
      }

      console.log('Sign out successful, updating auth state...');

      // Force a small delay to ensure all operations have completed
      await new Promise(resolve => setTimeout(resolve, 100));

      setAuthState({
        user: null,
        session: null,
        profile: null,
        isLoading: false,
        isAuthenticated: false,
        error: null,
        isImpersonating: false,
        originalUser: null,
      });

      console.log('Redirecting to login page...');
      // Navigation will be handled by the component that calls signOut
    } catch (error) {
      console.error('Error signing out:', error);
      toast.error("An error occurred while logging out");

      // Force auth state reset on error
      setAuthState({
        user: null,
        session: null,
        profile: null,
        isLoading: false,
        isAuthenticated: false,
        error: null,
        isImpersonating: false,
        originalUser: null,
      });

      // Navigation will be handled by the component that calls signOut
    }
  };

  const startImpersonation = async (userId: string) => {
    try {
      if (!authState.profile?.is_super_admin) {
        toast.error("Only super admins can impersonate users");
        return;
      }

      const originalUser = authState.user;

      console.log("Attempting to impersonate user:", userId);
      const impersonatedProfile = await fetchProfile(userId);
      if (!impersonatedProfile) {
        console.error("Failed to fetch impersonated user profile");
        toast.error("User profile not found");
        return;
      }

      console.log("Fetched impersonated profile:", impersonatedProfile);
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        toast.error("No active session found");
        return;
      }

      // Use the hardcoded Supabase URL from the client
      const SUPABASE_URL = "https://pwaeknalhosfwuxkpaet.supabase.co";
      console.log("Using Supabase URL:", SUPABASE_URL);

      console.log("Calling get-user-by-id function with userId:", userId);
      const response = await fetch(`${SUPABASE_URL}/functions/v1/get-user-by-id`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({ userId }),
      });

      if (!response.ok) {
        let errorMessage = "Failed to fetch user details";
        try {
          const errorData = await response.json();
          console.error("Error from edge function:", errorData);
          errorMessage = errorData.error || errorMessage;
        } catch (parseError) {
          console.error("Error parsing error response:", parseError, "Status:", response.status);
          errorMessage = `Server error (${response.status}): ${response.statusText}`;
        }
        toast.error(errorMessage);
        return;
      }

      console.log("Response status:", response.status);
      const responseText = await response.text();
      console.log("Raw response:", responseText);

      let userData;
      try {
        userData = JSON.parse(responseText);
        console.log("Parsed user data:", userData);
      } catch (parseError) {
        console.error("Error parsing response:", parseError);
        toast.error("Failed to parse user data");
        return;
      }

      if (!userData.user) {
        console.error("No user data in response");
        toast.error("User not found");
        return;
      }

      console.log("Setting impersonation state with user:", userData.user);

      // Store original user in localStorage as a backup
      try {
        localStorage.setItem('originalUser', JSON.stringify(originalUser));
        console.log("Original user backed up to localStorage");
      } catch (storageError) {
        console.warn("Failed to backup original user to localStorage:", storageError);
      }

      // Update the auth state with impersonation data
      const newAuthState = {
        user: userData.user,
        session: session,
        profile: impersonatedProfile,
        isLoading: false,
        isAuthenticated: true,
        error: null,
        isImpersonating: true,
        originalUser,
      };

      // Set the state
      setAuthState(newAuthState);

      // Also store in sessionStorage as a backup
      try {
        sessionStorage.setItem('impersonationState', JSON.stringify({
          isImpersonating: true,
          impersonatedUserId: userData.user.id,
          timestamp: new Date().toISOString()
        }));
      } catch (storageError) {
        console.warn("Failed to store impersonation state in sessionStorage:", storageError);
      }

      // Force a small delay to ensure state is updated
      await new Promise(resolve => setTimeout(resolve, 300));

      console.log("Impersonation state set successfully");
      // return true; // Indicate success - Removed to match Promise<void>
    } catch (error) {
      console.error('Error impersonating user:', error);
      toast.error("An unexpected error occurred during impersonation");
    }
  };

  const stopImpersonation = async () => {
    console.log("Stopping impersonation...");

    // Check if we're impersonating
    if (!authState.isImpersonating) {
      console.warn("Not currently impersonating.");
      toast.error("Cannot end impersonation: No active impersonation session");
      return;
    }

    // Get original user from state or localStorage backup
    let originalUser = authState.originalUser;

    // If original user is missing from state, try to get it from localStorage
    if (!originalUser) {
      try {
        const storedUser = localStorage.getItem('originalUser');
        if (storedUser) {
          originalUser = JSON.parse(storedUser);
          console.log("Restored original user from localStorage");
        }
      } catch (error) {
        console.error("Error restoring original user from localStorage:", error);
      }
    }

    // If we still don't have the original user, we can't continue
    if (!originalUser) {
      console.error("Missing original user data, cannot stop impersonation properly");
      toast.error("Cannot end impersonation: Missing original user data");

      // Force logout as a last resort
      await signOut();
      return;
    }

    console.log("Original user data:", originalUser);

    const originalUserId = originalUser.id;

    // Set loading state while restoring
    // Clear the localStorage and sessionStorage backups
    try {
      localStorage.removeItem('originalUser');
      sessionStorage.removeItem('impersonationState');
    } catch (error) {
      console.warn("Error removing impersonation data from storage:", error);
    }

    // Set loading state while restoring
    setAuthState(prev => ({
      ...prev,
      isLoading: true,
      isImpersonating: false, // Mark as not impersonating immediately
      user: originalUser, // Restore original user object
      originalUser: null, // Clear original user backup
    }));

    try {
      // Fetch the current session (should belong to the original user now)
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      if (sessionError || !sessionData.session) {
        console.error('Error getting session after stopping impersonation:', sessionError);
        // If session fails, sign out completely for safety
        await signOut();
        return;
      }

      // Fetch the original user's profile
      const originalProfile = await fetchProfile(originalUserId);
      if (!originalProfile) {
         console.error('Failed to fetch original user profile after stopping impersonation.');
         // Sign out if profile fetch fails
         await signOut();
         return;
      }

      // Set the final restored state
      setAuthState({
        user: originalUser, // Keep the restored user object
        session: sessionData.session,
        profile: originalProfile,
        isLoading: false,
        isAuthenticated: true,
        error: null,
        isImpersonating: false,
        originalUser: null,
      });

      // Force a small delay to ensure state is updated
      await new Promise(resolve => setTimeout(resolve, 100));

      console.log("Impersonation stopped successfully. Restored user:", originalUserId);
      toast.success("Returned to your original account.");

    } catch (error) {
      console.error('Error stopping impersonation:', error);
      toast.error("An error occurred while returning to your account. Please log out and back in.");
      // Attempt to sign out cleanly on error
      await signOut();
    }
  };

  // Utility function to check if impersonation is active
  const isImpersonationActive = () => {
    // First check the authState
    if (authState.isImpersonating && authState.originalUser) {
      return true;
    }

    // If not in authState, check sessionStorage as a backup
    try {
      const storedState = sessionStorage.getItem('impersonationState');
      if (storedState) {
        const parsedState = JSON.parse(storedState);
        // Only consider valid if it's recent (within last hour) and matches current user
        if (parsedState.isImpersonating &&
            parsedState.impersonatedUserId === authState.user?.id &&
            new Date().getTime() - new Date(parsedState.timestamp).getTime() < 3600000) {
          console.log("Detected impersonation from sessionStorage");
          return true;
        }
      }
    } catch (error) {
      console.warn("Error checking impersonation state in sessionStorage:", error);
    }

    return false;
  };

  const contextValue: AuthContextProps = {
    authState,
    signUp,
    signIn,
    signOut,
    startImpersonation,
    stopImpersonation,
    refreshProfile,
    isImpersonationActive,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
